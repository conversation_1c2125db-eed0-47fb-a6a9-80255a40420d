import { PostalCodeRepository } from "@domain/interfaces/PostalCodeRepository"
import { postalCodeApi } from "@infrastructure/api/postalCodeApi"
import { TPaginatedData } from "types/apiResponse.type"

export class PostalCodeApiRepository implements PostalCodeRepository {
  async getAll(filter?: any) {
    const resp = await postalCodeApi.getAll(filter)
    return Promise.resolve(resp?.data as TPaginatedData<any>)
  }

  async getById(id?: number | string) {
    const resp = await postalCodeApi.getById(id)
    return Promise.resolve(resp?.data as any)
  }

  async getIds(params: any) {
    const resp = await postalCodeApi.getIds(params)
    return Promise.resolve(resp?.data as any)
  }
}
