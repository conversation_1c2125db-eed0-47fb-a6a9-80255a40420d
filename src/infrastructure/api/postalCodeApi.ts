import { MiscConstant } from "@constants/misc"
import { httpClientConsole } from "@infrastructure/providers/httpClient"
import { convertToCamelCase } from "@utils/misc"
import { TApiResponse, TPaginatedData } from "types/apiResponse.type"

export const postalCodeApi = {
  getAll: async (params?: any): Promise<TApiResponse<TPaginatedData<any>>> => {
    try {
      const response = await httpClientConsole.get("/postal-code/get", {
        params,
      })
      return convertToCamelCase(response.data) as TApiResponse<
        TPaginatedData<any>
      >
    } catch (error) {
      return MiscConstant.OK_RESPONSE_PAGINATED_DATA_DEFAULT as TApiResponse<
        TPaginatedData<any>
      >
    }
  },

  getById: async (id?: number | string): Promise<TApiResponse<any>> => {
    const response = await httpClientConsole.get(`/postal-code/get/${id}`)
    return convertToCamelCase(response.data) as TApiResponse<any>
  },

  getIds: async (params: any): Promise<TApiResponse<any>> => {
    const response = await httpClientConsole.get(`/postal-code/get-ids`, {
      params,
      paramsSerializer: {
        indexes: null,
      },
    })
    return convertToCamelCase(response.data) as TApiResponse<any>
  },
}
