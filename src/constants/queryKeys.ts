/* eslint-disable @typescript-eslint/no-extraneous-class */
export class QueryKeys {
  static readonly BLOG_AND_NEWS = "BLOG_AND_NEWS"
  static readonly BLOG_AND_NEWS_CREATE = "BLOG_AND_NEWS_CREATE"
  static readonly BLOG_AND_NEWS_UPDATE = "BLOG_AND_NEWS_UPDATE"
  static readonly BLOG_AND_NEWS_DETAIL = "BLOG_AND_NEWS_DETAIL"
  static readonly BLOG_AND_NEWS_RELATED_ITEM_LIST =
    "BLOG_AND_NEWS_RELATED_ITEM_LIST"

  static readonly GET_ALL_CHANGE_LOGS = "GET_ALL_CHANGE_LOGS"

  static readonly WIZARD_MENU = "WIZARD_MENU"
  static readonly WIZARD_MENU_CREATE = "WIZARD_MENU_CREATE"
  static readonly WIZARD_MENU_UPDATE = "WIZARD_MENU_UPDATE"
  static readonly WIZARD_MENU_DELETE = "WIZARD_MENU_DELETE"
  static readonly WIZARD_MENU_DELETE_SECTION = "WIZARD_MENU_DELETE_SECTION"
  static readonly WIZARD_MENU_DETAIL = "WIZARD_MENU_DETAIL"
  static readonly WIZARD_SECTIONS = "WIZARD_SECTIONS"
  static readonly WIZARD_CONTENT_NAME = "WIZARD_CONTENT_NAME"
  static readonly WIZARD_CONTENT_OPTIONS = "WIZARD_CONTENT_OPTIONS"

  static readonly VOUCHER = "VOUCHER"
  static readonly VOUCHER_DETAIL = "VOUCHER_DETAIL"
  static readonly VOUCHER_CREATE = "VOUCHER_CREATE"
  static readonly VOUCHER_UPDATE = "VOUCHER_UPDATE"
  static readonly VOUCHER_DELETE = "VOUCHER_DELETE"

  static readonly ELIGIBILITY_OPTIONS = "ELIGIBILITY_OPTIONS"
  static readonly ELIGIBILITY_NAME = "ELIGIBILITY_NAME"

  static readonly ELIGIBLE_ENTITY = "ELIGIBLE_ENTITY"
  static readonly ELIGIBLE_ENTITY_DETAILS = "ELIGIBLE_ENTITY_DETAILS"

  static readonly GET_ALL_ROLES = "GET_ALL_ROLES"
  static readonly GET_ALL_ROLE_USERS = "GET_ALL_ROLE_USERS"
  static readonly GET_ALL_ROLE_ORGS = "GET_ALL_ROLE_ORGS"
  static readonly GET_ALL_ROLE_AUTHORITIES = "GET_ALL_ROLE_AUTHORITIES"
  static readonly GET_ALL_AUTHORITIES = "GET_ALL_AUTHORITIES"
  static readonly GET_ALL_USERS = "GET_ALL_USERS"
  static readonly GET_ALL_ORGS = "GET_ALL_ORGS"
  static readonly GET_ROLE_DETAIL = "GET_ROLE_DETAIL"

  static readonly RACK = "RACK"
  static readonly RACK_DETAIL = "RACK_DETAIL"
  static readonly RACK_CREATE = "RACK_CREATE"
  static readonly RACK_UPDATE = "RACK_UPDATE"
  static readonly RACK_DELETE = "RACK_DELETE"
  static readonly RACK_WAREHOUSE_OPTIONS = "RACK_WAREHOUSE_OPTIONS"

  static readonly JOB_TITLE_CREATE = "JOB_TITLE_CREATE"
  static readonly JOB_TITLE_UPDATE = "JOB_TITLE_UPDATE"
  static readonly JOB_TITLE_DETAIL = "JOB_TITLE_DETAIL"

  static readonly MEMBER = "MEMBER"
  static readonly MEMBER_DETAIL = "MEMBER_DETAIL"
  static readonly MEMBER_CREATE = "MEMBER_CREATE"
  static readonly MEMBER_UPDATE = "MEMBER_UPDATE"
  static readonly MEMBER_DELETE = "MEMBER_DELETE"

  static readonly GET_ALL_COUNTRIES = "GET_ALL_COUNTRIES"
  static readonly GET_COUNTRY_BY_ID = "GET_COUNTRY_BY_ID"
  static readonly COUNTRY_NAME = "COUNTRY_NAME"
  static readonly COUNTRY_OPTIONS = "COUNTRY_OPTIONS"

  static readonly GET_ALL_CATEGORIES = "GET_ALL_CATEGORIES"
  static readonly SUBCATEGORY_OPTIONS = "SUBCATEGORY_OPTIONS"
  static readonly CATEGORY_NAME = "CATEGORY_NAME"

  static readonly BRAND_OPTIONS = "BRAND_OPTIONS"
  static readonly BRAND_NAME = "BRAND_NAME"

  static readonly MEMBER_PREFERENCE = "MEMBER_PREFERENCE"
  static readonly MEMBER_PREFERENCE_CREATE = "MEMBER_PREFERENCE_CREATE"
  static readonly MEMBER_PREFERENCE_UPDATE = "MEMBER_PREFERENCE_UPDATE"
  static readonly MEMBER_PREFERENCE_DELETE = "MEMBER_PREFERENCE_DELETE"

  static readonly MEMBER_BALANCE = "MEMBER_BALANCE"
  static readonly BALANCE_CREATE = "BALANCE_CREATE"

  static readonly MEMBER_CART = "MEMBER_CART"
  static readonly CART_CREATE = "CART_CREATE"

  static readonly MEMBER_SELLER_VERIFICATION = "MEMBER_SELLER_VERIFICATION"
  static readonly SELLER_VERIFICATION_CREATE = "SELLER_VERIFICATION_CREATE"
  static readonly SELLER_VERIFICATION_UPDATE = "SELLER_VERIFICATION_UPDATE"
  static readonly GET_ALL_SELLER_VERIFICATION = "GET_ALL_SELLER_VERIFICATION"

  static readonly MEMBER_ADDRESS = "MEMBER_ADDRESS"
  static readonly ADDRESS_CREATE = "ADDRESS_CREATE"
  static readonly ADDRESS_UPDATE = "ADDRESS_UPDATE"

  static readonly MEMBER_BANK_ACCOUNT = "MEMBER_BANK_ACCOUNT"
  static readonly BANK_ACCOUNT_CREATE = "BANK_ACCOUNT_CREATE"
  static readonly BANK_ACCOUNT_UPDATE = "BANK_ACCOUNT_UPDATE"

  static readonly PROVINCE_OPTIONS = "PROVINCE_OPTIONS"
  static readonly PROVINCE_BY_ID = "PROVINCE_BY_ID"

  static readonly CITY_OPTIONS = "CITY_OPTIONS"
  static readonly CITY_BY_ID = "CITY_BY_ID"

  static readonly DISTRICT_OPTIONS = "DISTRICT_OPTIONS"
  static readonly DISTRICT_BY_ID = "DISTRICT_BY_ID"

  static readonly POSTAL_CODE_OPTIONS = "POSTAL_CODE_OPTIONS"
  static readonly POSTAL_CODE_IDS = "POSTAL_CODE_IDS"

  static readonly GET_ALL_PRODUCT_VARIANT_OPTIONS =
    "GET_ALL_PRODUCT_VARIANT_OPTIONS"

  static readonly GET_ALL_PRODUCT_VARIANT_FILTER_OPTIONS =
    "GET_ALL_PRODUCT_VARIANT_FILTER_OPTIONS"

  static readonly GET_ALL_SELLER_LISTING_OPTIONS =
    "GET_ALL_SELLER_LISTING_OPTIONS"

  static readonly GET_ALL_SELLER_LISTING_FILTER_OPTIONS =
    "GET_ALL_SELLER_LISTING_FILTER_OPTIONS"

  static readonly GET_PRODUCT_COUNTRY_ID = "GET_PRODUCT_COUNTRY_ID"

  static readonly GET_ALL_SIZE_CHART_OPTIONS = "GET_ALL_SIZE_CHART_OPTIONS"
  static readonly GET_ALL_SIZE_CHART_FILTER_OPTIONS =
    "GET_ALL_SIZE_CHART_OPTIONS"

  static readonly GET_SUB_CATEGORIES = "GET_SUB_CATEGORIES"
  static readonly ITEM_DETAIL = "ITEM_DETAIL"

  static readonly ITEM_STOCK = "ITEM_STOCK"
  static readonly STOCK_LEVEL_SHIPPING = "STOCK_LEVEL_SHIPPING"
  static readonly STOCK_LEVEL_LISTING = "STOCK_LEVEL_LISTING"

  static readonly GET_ALL_API_MANAGEMENT = "GET_ALL_API_MANAGEMENT"
  static readonly GET_API_MANAGEMENT_DETAIL = "GET_API_MANAGEMENT_DETAIL"

  static readonly GET_ALL_MEMBERS = "GET_ALL_MEMBERS"

  static readonly GET_ALL_BANNER_SLIDER_OPTIONS =
    "GET_ALL_BANNER_SLIDER_OPTIONS"

  static readonly GET_ALL_POPUP_BANNER_OPTIONS = "GET_ALL_POPUP_BANNER_OPTIONS"
  static readonly GET_ALL_POPUP_BANNERS = "GET_ALL_POPUP_BANNERS"

  static readonly GET_ALL_FEE = "GET_ALL_FEE"
  static readonly GET_FEE_DETAIL = "GET_FEE_DETAIL"

  static readonly GET_ALL_RACKS = "GET_ALL_RACKS"

  static readonly GET_ALL_BRANDS = "GET_ALL_BRANDS"
  static readonly GET_ALL_FAQS = "GET_ALL_FAQS"
  static readonly GET_FAQ_DETAIL = "GET_FAQ_DETAIL"

  static readonly USER_DETAIL = "USER_DETAIL"
  static readonly GET_ALL_JOB_TITLE_USER = "GET_ALL_JOB_TITLE_USER"
  static readonly GET_ALL_ORDERS = "GET_ALL_ORDERS"

  static readonly GET_ALL_PAYMENT_METHODS = "GET_ALL_PAYMENT_METHODS"
  static readonly GET_PAYMENT_METHOD_BY_IDS = "GET_PAYMENT_METHOD_BY_IDS"
  static readonly GET_PAYMENT_METHOD = "GET_PAYMENT_METHOD"

  static readonly GET_VOUCHER_BY_IDS = "GET_VOUCHER_BY_IDS"
  static readonly GET_VOUCHER_BY_CODES = "GET_VOUCHER_BY_CODES"

  static readonly GET_UNIQUE_SIZE = "GET_UNIQUE_SIZE"

  static readonly GET_CATEGORY_BY_IDS = "GET_CATEGORY_BY_IDS"
  static readonly GET_SUBCATEGORY_BY_IDS = "GET_SUBCATEGORY_BY_IDS"

  static readonly GET_ORDER_BY_ID = "GET_ORDER_BY_ID"

  static readonly GET_ORDER_NOTE_BY_ORDER_ID = "GET_ORDER_NOTE_BY_ORDER_ID"
  static readonly GET_ORDER_NOTES = "GET_ORDER_NOTES"
  static readonly GET_OUTSTANDING_ORDERS = "GET_OUTSTANDING_ORDERS"

  static readonly GET_ALL_QC_ORDERS = "GET_ALL_QC_ORDERS"
  static readonly GET_ALL_LC_ORDERS = "GET_ALL_LC_ORDERS"

  static readonly GET_ALL_DELIVERY_ORDERS = "GET_ALL_DELIVERY_ORDERS"
  static readonly GET_ALL_PROCESSED_ORDERS = "GET_ALL_PROCESSED_ORDERS"

  static readonly GET_ALL_FAILED_ORDERS = "GET_ALL_FAILED_ORDERS"

  static readonly GET_ALL_OFFERS = "GET_ALL_OFFERS"
  static readonly GET_OFFER_BY_ID = "GET_OFFER_BY_ID"
  static readonly GET_OFFER_NOTE_BY_OFFER_ID = "GET_OFFER_NOTE_BY_OFFER_ID"

  static readonly GET_ALL_CONSIGNMENTS = "GET_ALL_CONSIGNMENTS"
  static readonly GET_MEMBER_BY_IDS = "GET_MEMBER_BY_IDS"
  static readonly GET_RACK_BY_IDS = "GET_RACK_BY_IDS"
  static readonly GET_CONSIGNMENT_BY_ID = "GET_CONSIGNMENT_BY_ID"
  static readonly GET_CONSIGNMENT_NOTE_BY_CONSIGNMENT_ID =
    "GET_CONSIGNMENT_NOTE_BY_CONSIGNMENT_ID"

  static readonly GET_OUTSTANDING_CONSIGNMENTS = "GET_OUTSTANDING_CONSIGNMENTS"
  static readonly GET_QC_CONSIGNMENTS = "GET_QC_CONSIGNMENTS"
  static readonly GET_LC_CONSIGNMENTS = "GET_LC_CONSIGNMENTS"
  static readonly GET_RACK_ASSIGN_CONSIGNMENTS = "GET_RACK_ASSIGN_CONSIGNMENTS"
  static readonly GET_TO_PRE_DELIVERY_CONSIGNMENTS =
    "GET_TO_PRE_DELIVERY_CONSIGNMENTS"

  static readonly GET_TO_DELIVERY_CONSIGNMENTS = "GET_TO_DELIVERY_CONSIGNMENTS"

  static readonly GET_FAILED_CONSIGNMENTS = "GET_FAILED_CONSIGNMENTS"
  static readonly GET_ALL_ORDER_BULK_UPLOAD_JOB =
    "GET_ALL_ORDER_BULK_UPLOAD_JOB"

  static readonly GET_ORDER_BULK_UPLOAD_JOB_BY_ID =
    "GET_ORDER_BULK_UPLOAD_JOB_BY_ID"

  static readonly GET_ALL_SELLER_LISTING = "GET_ALL_SELLER_LISTING"
  static readonly GET_ALL_ITEM = "GET_ALL_ITEM"

  static readonly GET_MEMBER_PLATFORM_FEE = "GET_MEMBER_PLATFORM_FEE"

  static readonly GET_ALL_COLLECTION = "GET_ALL_COLLECTION"
  static readonly GET_ALL_BRAND = "GET_ALL_BRAND"
  static readonly GET_ALL_SUB_CATEGORIES = "GET_ALL_SUB_CATEGORIES"
  static readonly GET_SELLER_LISTING_DETAIL = "GET_SELLER_LISTING_DETAIL"

  static readonly GET_SIZE_CHART_BY_ID = "GET_SIZE_CHART_BY_ID"

  static readonly GET_ALL_SIZE_CHART = "GET_ALL_SIZE_CHART"

  static readonly SHIPPING_FEE_RATES = "SHIPPING_FEE_RATES"

  static readonly GET_PLATFORM_FEE = "GET_PLATFORM_FEE"
}
