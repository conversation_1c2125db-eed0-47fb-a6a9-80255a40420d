import {
  IconSearchOutline,
  Input,
  TInputProps,
} from "@kickavenue/ui/components"
import debounce from "lodash/debounce"
import React, { forwardRef, useRef } from "react"

import InputCloseIcon from "./InputCloseIcon"

export interface SearchInputProps extends TInputProps {
  onClearText: () => void
  debounce?: boolean
  debounceTime?: number
}

const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  (props, ref) => {
    const {
      onClearText,
      debounce: useDebounce = false,
      debounceTime = 300,
      onChange,
      ...rest
    } = props

    const debouncedOnChange = useRef(
      debounce((value: string) => {
        // Create a synthetic event to maintain consistency
        const syntheticEvent = {
          target: { value },
          currentTarget: { value },
        } as React.ChangeEvent<HTMLInputElement>
        onChange?.(syntheticEvent)
      }, debounceTime),
    )

    // Cleanup debounced function on unmount or when dependencies change
    React.useEffect(() => {
      return () => {
        debouncedOnChange.current.cancel()
      }
    }, [debouncedOnChange])

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (useDebounce) {
        // Pass the value instead of the event to avoid stale event references
        debouncedOnChange.current(event.target.value)
      } else {
        onChange?.(event)
      }
    }

    return (
      <Input
        ref={ref}
        leftIcon={<IconSearchOutline />}
        rightIcon={
          <InputCloseIcon text={props.value as string} onClick={onClearText} />
        }
        size="sm"
        placeholder="Search"
        containerInputProps={{
          className: "!bg-gray-w-95 !border-none hover:border-none !w-full",
        }}
        {...rest}
        value={props.value}
        onChange={handleChange}
      />
    )
  },
)

SearchInput.displayName = "SearchInput"

export default SearchInput
