import {
  IconSearchOutline,
  Input,
  TInputProps,
} from "@kickavenue/ui/components"
import debounce from "lodash/debounce"
import React, { forwardRef, useMemo } from "react"

import InputCloseIcon from "./InputCloseIcon"

export interface SearchInputProps extends TInputProps {
  onClearText: () => void
  debounce?: boolean
  debounceTime?: number
}

const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  (props, ref) => {
    const {
      onClearText,
      debounce: useDebounce = false,
      debounceTime = 300,
      onChange,
      ...rest
    } = props

    const debouncedOnChange = useMemo(
      () =>
        debounce((event: React.ChangeEvent<HTMLInputElement>) => {
          onChange?.(event)
        }, debounceTime),
      [onChange, debounceTime],
    )

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (useDebounce) {
        debouncedOnChange(event)
      } else {
        onChange?.(event)
      }
    }

    return (
      <Input
        ref={ref}
        leftIcon={<IconSearchOutline />}
        rightIcon={
          <InputCloseIcon text={props.value as string} onClick={onClearText} />
        }
        size="sm"
        placeholder="Search"
        containerInputProps={{
          className: "!bg-gray-w-95 !border-none hover:border-none !w-full",
        }}
        {...rest}
        value={props.value}
        onChange={handleChange}
      />
    )
  },
)

SearchInput.displayName = "SearchInput"

export default SearchInput
