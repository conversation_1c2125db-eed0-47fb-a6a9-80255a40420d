import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import SearchInput from '../SearchInput'

// Mock the UI components
jest.mock('@kickavenue/ui/components', () => ({
  IconSearchOutline: () => <div data-testid="search-icon" />,
  Input: React.forwardRef<HTMLInputElement, any>(({ onChange, value, leftIcon, rightIcon, ...props }, ref) => (
    <div>
      {leftIcon}
      <input
        ref={ref}
        value={value}
        onChange={onChange}
        data-testid="search-input"
        {...props}
      />
      {rightIcon}
    </div>
  )),
}))

// Mock InputCloseIcon
jest.mock('../InputCloseIcon', () => {
  return function MockInputCloseIcon({ text, onClick }: { text: string; onClick: () => void }) {
    if (!text?.length) return null
    return <button data-testid="clear-button" onClick={onClick}>Clear</button>
  }
})

describe('SearchInput', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render correctly', () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClearText={mockOnClearText}
      />
    )

    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('should call onChange immediately when debounce is disabled', () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClearText={mockOnClearText}
        debounce={false}
      />
    )

    const input = screen.getByTestId('search-input')
    fireEvent.change(input, { target: { value: 'test' } })

    expect(mockOnChange).toHaveBeenCalledTimes(1)
    expect(mockOnChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: 'test' })
      })
    )
  })

  it('should debounce onChange when debounce is enabled', async () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClearText={mockOnClearText}
        debounce={true}
        debounceTime={100}
      />
    )

    const input = screen.getByTestId('search-input')
    
    // Type multiple characters quickly
    fireEvent.change(input, { target: { value: 't' } })
    fireEvent.change(input, { target: { value: 'te' } })
    fireEvent.change(input, { target: { value: 'test' } })

    // Should not be called immediately
    expect(mockOnChange).not.toHaveBeenCalled()

    // Wait for debounce
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledTimes(1)
    }, { timeout: 200 })

    expect(mockOnChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: 'test' })
      })
    )
  })

  it('should show clear button when there is text and call onClearText when clicked', () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    render(
      <SearchInput
        value="test"
        onChange={mockOnChange}
        onClearText={mockOnClearText}
      />
    )

    const clearButton = screen.getByTestId('clear-button')
    expect(clearButton).toBeInTheDocument()

    fireEvent.click(clearButton)
    expect(mockOnClearText).toHaveBeenCalledTimes(1)
  })

  it('should not show clear button when there is no text', () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClearText={mockOnClearText}
      />
    )

    expect(screen.queryByTestId('clear-button')).not.toBeInTheDocument()
  })

  it('should handle clearing and then typing new text correctly', async () => {
    const mockOnChange = jest.fn()
    const mockOnClearText = jest.fn()

    const { rerender } = render(
      <SearchInput
        value="initial"
        onChange={mockOnChange}
        onClearText={mockOnClearText}
        debounce={true}
        debounceTime={100}
      />
    )

    // Clear the text
    const clearButton = screen.getByTestId('clear-button')
    fireEvent.click(clearButton)
    expect(mockOnClearText).toHaveBeenCalledTimes(1)

    // Simulate parent component clearing the value
    rerender(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClearText={mockOnClearText}
        debounce={true}
        debounceTime={100}
      />
    )

    // Now type new text
    const input = screen.getByTestId('search-input')
    fireEvent.change(input, { target: { value: 'new text' } })

    // Wait for debounce
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: 'new text' })
        })
      )
    }, { timeout: 200 })
  })
})
