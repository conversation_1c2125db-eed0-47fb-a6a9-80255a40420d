"use client"

import { use<PERSON><PERSON>back, useEffect, useMemo } from "react"
import { useInView } from "react-intersection-observer"

import Modal from "@components/shared/Modal"
import ModalCancelConfirm from "@components/shared/ModalParts/ModalCancelConfirm"
import ModalHeader from "@components/shared/ModalParts/ModalHeader"
import SearchInput from "@components/shared/SearchInput"
import Spinner from "@components/shared/Spinner"
import { ModalConstant } from "@constants/modal"
import { useCashierStore } from "stores/cashierStore"
import { useModalStore } from "stores/modalStore"
import { TSellerListing } from "types/sellerListing.type"

import useFetchCashierProducts from "../hook/useFetchCashierProducts"
import useProductsTable from "../hook/useProductsTable"

import CashierProductsModalAddFilter from "./CashierProductsModalAddFilter"
import CashierProductsModalAddProducts from "./CashierProductsModalAddProducts"
import CashierProductsModalAddSelectedProducts from "./CashierProductsModalAddSelectedProducts"

const { CASHIER_PRODUCTS_ADD } = ModalConstant.MODAL_IDS

const CashierProductsModalAdd = () => {
  const { open, modalId, setOpen } = useModalStore()
  const {
    sellerListing: { selectedRowKeys, filter, selectedListings },
    setSellerListingSelectedRowKeys,
    setSellerListingFilter,
    setSelectedListings,
  } = useCashierStore()
  const isOpen = open && modalId === CASHIER_PRODUCTS_ADD
  const { ref, inView } = useInView()

  const { products, isLoading, hasNextPage, fetchNextPage } =
    useFetchCashierProducts(filter, isOpen)

  const disableConfirm = useMemo(() => {
    if (isLoading) return true
    if (!selectedRowKeys.length && !selectedListings.length) return true
    return false
  }, [isLoading, selectedRowKeys, selectedListings])

  const searchValue = useMemo(() => {
    return filter?.search || ""
  }, [filter?.search])

  const { columns } = useProductsTable({
    data: products,
    selectedRowKeys,
    setSelectedRowKeys: setSellerListingSelectedRowKeys,
  })

  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSellerListingFilter({
        ...filter,
        search: e.target.value,
      })
    },
    [filter, setSellerListingFilter],
  )

  const onClearSearch = useCallback(() => {
    setSellerListingFilter({
      ...filter,
      search: "",
    })
  }, [filter, setSellerListingFilter])

  const handleConfirmClick = useCallback(() => {
    setSelectedListings(
      products?.filter((product) => selectedRowKeys.includes(product.id)) || [],
    )
    setOpen(false, CASHIER_PRODUCTS_ADD)
  }, [setSelectedListings, products, setOpen, selectedRowKeys])

  useEffect(() => {
    if (inView) {
      fetchNextPage()
    }
  }, [inView, fetchNextPage])

  if (!isOpen) return null

  return (
    <Modal
      modalId={CASHIER_PRODUCTS_ADD}
      className="relative !my-0 max-h-[95vh] sm:!max-w-[1036px]"
    >
      <ModalHeader
        title="Add Product"
        onClose={() => setOpen(false, CASHIER_PRODUCTS_ADD)}
      />
      <div className="flex h-full max-h-[85vh] flex-col gap-base overflow-y-auto p-lg">
        <CashierProductsModalAddFilter />
        <SearchInput
          value={searchValue}
          placeholder="Search"
          debounce
          debounceTime={500}
          onClearText={onClearSearch}
          onChange={handleSearch}
        />
        <CashierProductsModalAddSelectedProducts
          selectedRowKeys={selectedRowKeys}
          hidden={!selectedRowKeys.length || isLoading}
        />
        <CashierProductsModalAddProducts
          rowKey="id"
          isLoading={isLoading}
          columns={columns}
          dataSource={products as TSellerListing[]}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys) =>
              setSellerListingSelectedRowKeys(selectedRowKeys as number[]),
          }}
        />
        {hasNextPage && (
          <div ref={ref} className="flex justify-center">
            <Spinner />
          </div>
        )}
      </div>
      <ModalCancelConfirm
        disableConfirm={disableConfirm}
        onConfirm={handleConfirmClick}
        onCancel={() => setOpen(false, CASHIER_PRODUCTS_ADD)}
      />
    </Modal>
  )
}

export default CashierProductsModalAdd
