import { useCallback, useEffect, useState } from "react"

import { useCashierStore } from "stores/cashierStore"
import {
  ESellerListingQuantityType,
  TSellerListingFilter,
} from "types/sellerListing.type"

const defaultFilter = {
  shippingMethod: "all-shipping-method",
  sizeId: [0],
  itemCondition: "all-item-condition",
  packagingCondition: "all-packaging-condition",
  hasQuantity: true,
  quantityValue: ESellerListingQuantityType.HasQuantity,
  search: null,
}

const useCashierProductsFilter = () => {
  const {
    sellerListing: { filter },
    setSellerListingFilter,
    clearSellerListingFilter,
    clearSellerListing,
  } = useCashierStore()

  const [draftFilter, setDraftFilter] =
    useState<Partial<TSellerListingFilter>>(defaultFilter)

  const handleItemSelect = useCallback(
    (key: keyof TSellerListingFilter, value: string | number[] | boolean) => {
      if (key === "hasQuantity") {
        setDraftFilter({
          ...draftFilter,
          quantityValue: value as ESellerListingQuantityType,
          hasQuantity: value === ESellerListingQuantityType.HasQuantity,
        })
        return
      }

      setDraftFilter({
        ...draftFilter,
        [key]: value,
      })
    },
    [draftFilter, setDraftFilter],
  )

  const handleApplyFilter = useCallback(() => {
    setSellerListingFilter(draftFilter as TSellerListingFilter)
  }, [draftFilter, setSellerListingFilter])

  const resetFilter = useCallback(() => {
    clearSellerListingFilter()
    clearSellerListing()
    setDraftFilter(defaultFilter)
    setSellerListingFilter(defaultFilter)
  }, [
    clearSellerListingFilter,
    clearSellerListing,
    setDraftFilter,
    setSellerListingFilter,
  ])

  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log("draftFilter ", draftFilter)
  }, [draftFilter])

  return {
    filter,
    draftFilter,
    handleItemSelect,
    handleApplyFilter,
    resetFilter,
  }
}

export default useCashierProductsFilter
