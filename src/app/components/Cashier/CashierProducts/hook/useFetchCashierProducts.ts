import { useInfiniteQuery } from "@tanstack/react-query"
import { useMemo } from "react"

import { GetAllSellerListing } from "@application/usecases/getAllSellerListing"
import { QueryKeys } from "@constants/queryKeys"
import { SellerListingApiRepository } from "@infrastructure/repositories/sellerListingApiRepository"
import { SellerListingStatus } from "types/misc.type"
import {
  ESellerListingShippingMethod,
  TSellerListingFilter,
} from "types/sellerListing.type"

const useFetchCashierProducts = (
  filter?: TSellerListingFilter,
  enabled = true,
) => {
  const fetchSellerListing = ({ pageParams }: { pageParams: number }) => {
    const r = new SellerListingApiRepository()
    const u = new GetAllSellerListing(r)
    const newParams: any = {
      ...filter,
      isExpired: false,
      isActive: true,
      status: SellerListingStatus.ListingApproved,
      itemIsActive: true,
      itemIsNonPurchaseable: false,
      page: pageParams,
      pageSize: filter?.pageSize || 10,
      sizeId: filter?.sizeId?.filter((id) => id !== 0),
    }

    if (filter?.itemCondition === "all-item-condition") {
      delete newParams.itemCondition
    }

    if (filter?.packagingCondition === "all-packaging-condition") {
      delete newParams.packagingCondition
    }

    if (filter?.shippingMethod === "all-shipping-method") {
      delete newParams.shippingMethod
    }

    if (filter?.shippingMethod === ESellerListingShippingMethod.Express) {
      newParams.isConsignment = true
      newParams.isPreOrder = false
    }

    if (filter?.shippingMethod === ESellerListingShippingMethod.PreOrder) {
      newParams.isConsignment = false
      newParams.isPreOrder = true
    }

    if (filter?.shippingMethod === ESellerListingShippingMethod.Standard) {
      newParams.isConsignment = false
      newParams.isPreOrder = false
    }

    // delete unused params
    delete newParams?.quantityValue

    return u.execute(newParams)
  }

  const query = useInfiniteQuery({
    queryKey: [
      QueryKeys.GET_ALL_SELLER_LISTING,
      ...Object.entries(filter ?? {}).map(([key, value]) => `${key}-${value}`),
    ],
    initialPageParam: 0,
    queryFn: ({ pageParam }) => {
      return fetchSellerListing({ pageParams: pageParam })
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.page < lastPage.totalPages - 1) {
        return lastPage.page + 1
      }
      return null
    },
    enabled,
  })

  const mappedData = useMemo(
    () => query?.data?.pages.map((page) => page.content).flat(),
    [query?.data],
  )

  return { products: mappedData, ...query }
}

export default useFetchCashierProducts
