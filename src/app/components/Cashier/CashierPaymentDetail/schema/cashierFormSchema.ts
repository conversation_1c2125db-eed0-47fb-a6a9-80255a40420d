/* eslint-disable max-lines-per-function */

import { z } from "zod"

import { FormField } from "@constants/formField"
import { ECashierShippingMethod, ECashierType } from "types/cashier.type"
import { PaymentMethod } from "types/misc.type"

const {
  WAREHOUSE,
  MARKETPLACE,
  EVENT,

  BUYER_EMAIL,
  BUYER_MOBILE_COUNTRY_NUMBER,
  BUYER_MOBILE_NUMBER,
  SHIPPING_METHOD,

  RECIPIENT_NAME,
  PROVINCE,
  CITY,
  DISTRICT,
  POSTAL_CODE,
  ADDRESS,
  NOTES,

  PAYMENT_METHOD,
  CREDIT_ENABLED,
  KICK_POINT_ENABLED,

  REFERENCE_NUMBER,
  AMOUNT_RECEIVED,
  VOUCHER_CODE,
  REMARK,
} = FormField.CASHIER

// Define the schema
export const generateCashierFormSchema = ({
  cashierType,
  totalPurchase,
}: {
  cashierType?: ECashierType | null
  totalPurchase?: number
}) => {
  return z
    .object({
      [WAREHOUSE.KEY]: z
        .object({
          label: z.string(),
          value: z.string(),
        })
        .nullable()
        .refine((val) => {
          if (cashierType === ECashierType.Store) {
            return val !== null
          }
          return true
        }, "Warehouse is required"),
      [MARKETPLACE.KEY]: z
        .object({
          label: z.string(),
          value: z.string(),
        })
        .nullable()
        .refine((val) => {
          if (cashierType === ECashierType.Marketplace) {
            return val !== null
          }
          return true
        }, "Marketplace is required"),
      [EVENT.KEY]: z.string().refine((val) => {
        if (cashierType === ECashierType.Event) {
          return val !== ""
        }
        return true
      }, "Event is required"),

      [BUYER_EMAIL.KEY]: z
        .object({
          label: z.string(),
          value: z.string(),
        })
        .nullable()
        .refine((val) => val !== null, "Buyer email is required"),
      [BUYER_MOBILE_COUNTRY_NUMBER.KEY]: z
        .object({
          label: z.string(),
          value: z.string(),
        })
        .nullable()
        .refine(
          (val) => val !== null,
          "Buyer mobile country number is required",
        ),
      [BUYER_MOBILE_NUMBER.KEY]: z
        .string()
        .min(1, "Buyer mobile number is required")
        .regex(/^\d+$/, "Mobile number must contain only digits"),
      [SHIPPING_METHOD.KEY]: z.string().min(1, "Shipping method is required"),

      [RECIPIENT_NAME.KEY]: z.string().optional(),
      [PROVINCE.KEY]: z.any().optional(),
      [CITY.KEY]: z.any().optional(),
      [DISTRICT.KEY]: z.any().optional(),
      [POSTAL_CODE.KEY]: z.any().optional(),
      [ADDRESS.KEY]: z.string().optional(),
      [NOTES.KEY]: z.string().optional(),

      [PAYMENT_METHOD.KEY]: z
        .object({
          label: z.string(),
          value: z.string(),
        })
        .nullable()
        .refine((val) => val !== null, "Payment method is required"),
      [KICK_POINT_ENABLED.KEY]: z.boolean().optional(),
      [CREDIT_ENABLED.KEY]: z.boolean().optional(),

      [REFERENCE_NUMBER.KEY]: z.string().optional(),
      [AMOUNT_RECEIVED.KEY]: z
        .string()
        .optional()
        .transform((val) => {
          if (typeof val === "string") {
            const num = Number(val.split(",").join(""))
            return isNaN(num) ? val : num
          }
          return val
        }),
      [VOUCHER_CODE.KEY]: z.any().optional(),
      [REMARK.KEY]: z.string().optional(),
    })
    .refine(
      (data) => {
        // If shipping method is delivery, require recipient name, province, city, postal code, and address
        if (data[SHIPPING_METHOD.KEY] === ECashierShippingMethod.Delivery) {
          return (
            Boolean(data[RECIPIENT_NAME.KEY]) &&
            Boolean(data[PROVINCE.KEY]) &&
            Boolean(data[CITY.KEY]) &&
            Boolean(data[DISTRICT.KEY]) &&
            Boolean(data[POSTAL_CODE.KEY]) &&
            Boolean(data[ADDRESS.KEY]) &&
            Boolean(data[NOTES.KEY])
          )
        }
        return true
      },
      {
        message: "Delivery information is required when delivery is selected",
        path: [SHIPPING_METHOD.KEY],
      },
    )
    .refine(
      (data) => {
        const paymentMethod = data[PAYMENT_METHOD.KEY]?.value.split("-")?.[1]
        const isPaymentMethodCC = paymentMethod === PaymentMethod.CreditCard
        const isPaymentMethodDebit = paymentMethod === PaymentMethod.DebitCard
        const isPaymentMethodVA = paymentMethod === PaymentMethod.VirtualAccount
        const isPaymentMethodQris = paymentMethod === PaymentMethod.Installments

        // If payment method is not cash, require reference number
        if (
          isPaymentMethodCC ||
          isPaymentMethodDebit ||
          isPaymentMethodVA ||
          isPaymentMethodQris
        ) {
          return Boolean(data[REFERENCE_NUMBER.KEY])
        }
        return true
      },
      {
        message: "Reference number is required",
        path: [REFERENCE_NUMBER.KEY],
      },
    )
    .refine(
      (data) => {
        // If payment method is cash, validate amount received
        const paymentMethod = data[PAYMENT_METHOD.KEY]?.value.split("-")?.[1]
        if (paymentMethod === PaymentMethod.Cash) {
          const amountReceived = data[AMOUNT_RECEIVED.KEY]

          if (!amountReceived) {
            return false
          }

          const numericValue =
            typeof amountReceived === "string"
              ? Number(amountReceived)
              : amountReceived

          if (isNaN(numericValue)) {
            return false
          }

          // Validate against totalPurchase if provided
          if (totalPurchase !== undefined && numericValue !== totalPurchase) {
            return false
          }
        }
        return true
      },
      {
        message: "Amount received must be equal to total purchase",
        path: [AMOUNT_RECEIVED.KEY],
      },
    )
}
