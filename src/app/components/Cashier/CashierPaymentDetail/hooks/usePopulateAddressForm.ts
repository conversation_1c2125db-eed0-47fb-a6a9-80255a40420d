import { useEffect } from "react"
import { useFormContext } from "react-hook-form"

import useFetchCityById from "@app/hooks/useFetchCityById"
import useFetchDistrictById from "@app/hooks/useFetchDistrictById"
import useFetchMemberAddress from "@app/hooks/useFetchMemberAddress"
import useFetchProvinceById from "@app/hooks/useFetchProvinceById"
import { FormField } from "@constants/formField"
import { useCashierFormStore } from "stores/cashierFormStore"

const { PROVINCE, CITY, DISTRICT, POSTAL_CODE, ADDRESS, NOTES } =
  FormField.CASHIER

const usePopulateAddressForm = (buyerId?: number) => {
  const { setValue, trigger } = useFormContext()
  const { setBuyerAddress } = useCashierFormStore()

  // START === set buyer address ===========================================
  const { primaryAddress } = useFetchMemberAddress(buyerId)

  useEffect(() => {
    if (!primaryAddress) return

    setBuyerAddress(primaryAddress)
    setValue(ADDRESS.KEY, primaryAddress.address ?? "")
    setValue(NOTES.KEY, primaryAddress.notes ?? "")

    trigger(ADDRESS.KEY)
    trigger(NOTES.KEY)
  }, [primaryAddress, setValue, setBuyerAddress, trigger])
  // END === set buyer address =============================================

  // START === set province data ==========================================
  const { data: provinceData } = useFetchProvinceById(
    primaryAddress?.provinceId ?? 0,
  )

  useEffect(() => {
    if (!provinceData) return

    setValue(
      PROVINCE.KEY,
      {
        value: provinceData.id,
        label: provinceData.name,
      },
      { shouldValidate: true },
    )
  }, [provinceData, setValue])
  // END === set province data ============================================

  // START === set city data ==============================================
  const { data: cityData } = useFetchCityById(primaryAddress?.cityId ?? 0)

  useEffect(() => {
    if (!cityData) return

    setValue(
      CITY.KEY,
      {
        value: cityData.id,
        label: cityData.name,
      },
      { shouldValidate: true },
    )
  }, [cityData, setValue])
  // END === set city data ================================================

  // START === set district data ==============================================
  const { data: districtData } = useFetchDistrictById(
    primaryAddress?.districtId ?? 0,
  )

  useEffect(() => {
    if (!districtData) return

    setValue(
      DISTRICT.KEY,
      {
        value: districtData.id,
        label: districtData.name,
      },
      { shouldValidate: true },
    )
  }, [districtData, setValue])
  // END === set district data ================================================

  // START === set postal code data ==============================================
  useEffect(() => {
    if (!primaryAddress?.zipCode) return

    setValue(
      POSTAL_CODE.KEY,
      {
        value: primaryAddress?.zipCode,
        label: primaryAddress?.zipCode,
      },
      { shouldValidate: true },
    )
  }, [setValue, primaryAddress?.zipCode])
  // END === set postal code data ================================================
}

export default usePopulateAddressForm
