/* eslint-disable max-lines-per-function */

import { zodResolver } from "@hookform/resolvers/zod"
import { useQueryClient } from "@tanstack/react-query"
import { usePathname } from "next/navigation"
import { useCallback, useEffect, useMemo } from "react"
import { FieldErrors, useForm } from "react-hook-form"
import { z } from "zod"

import useToast from "@app/hooks/useToast"
import { FormField } from "@constants/formField"
import { QueryKeys } from "@constants/queryKeys"
import { useCashierFormStore } from "stores/cashierFormStore"
import { useCashierStore } from "stores/cashierStore"
import { ECashierShippingMethod } from "types/cashier.type"

import { generateCashierFormSchema } from "../schema/cashierFormSchema"

import useCashierFormMutation from "./useCashierFormMutation"
import useGetCashierType from "./useGetCashierType"

const {
  PAYMENT_METHOD,
  KICK_POINT_ENABLED,
  MARKETPLACE,
  SHIPPING_METHOD,
  RECIPIENT_NAME,
  PROVINCE,
  CITY,
  DISTRICT,
  POSTAL_CODE,
  ADDRESS,
  NOTES,
  REFERENCE_NUMBER,
  AMOUNT_RECEIVED,
  CREDIT_ENABLED,
  VOUCHER_CODE,
  REMARK,
  WAREHOUSE,
  EVENT,
  BUYER_EMAIL,
  BUYER_MOBILE_COUNTRY_NUMBER,
  BUYER_MOBILE_NUMBER,
} = FormField.CASHIER

const formDefaultValues = {
  [WAREHOUSE.KEY]: null,
  [MARKETPLACE.KEY]: null,
  [EVENT.KEY]: "",

  [BUYER_EMAIL.KEY]: null,
  [BUYER_MOBILE_COUNTRY_NUMBER.KEY]: null,
  [BUYER_MOBILE_NUMBER.KEY]: "",
  [SHIPPING_METHOD.KEY]: "",

  [RECIPIENT_NAME.KEY]: "",
  [PROVINCE.KEY]: null,
  [CITY.KEY]: null,
  [DISTRICT.KEY]: null,
  [POSTAL_CODE.KEY]: null,
  [ADDRESS.KEY]: "",
  [NOTES.KEY]: "",

  [PAYMENT_METHOD.KEY]: null,
  [REFERENCE_NUMBER.KEY]: "",
  [AMOUNT_RECEIVED.KEY]: "",

  [KICK_POINT_ENABLED.KEY]: false,
  [CREDIT_ENABLED.KEY]: false,
  [VOUCHER_CODE.KEY]: null,
  [REMARK.KEY]: "",
}

const useCashierForm = () => {
  const pathname = usePathname()
  const queryClient = useQueryClient()

  const {
    sellerListing: { selectedListings },
    setSelectedListings,
    clearSellerListing,
  } = useCashierStore()
  const { setShowToast } = useToast()
  const cashierType = useGetCashierType()
  const {
    totalPurchase,
    setBuyerData,
    setBuyerAddress,
    setProcessingFeeAmount,
  } = useCashierFormStore()

  const cashierFormSchema = useMemo(
    () =>
      generateCashierFormSchema({
        cashierType: cashierType.value,
        totalPurchase,
      }),
    [cashierType.value, totalPurchase],
  )
  type CashierFormData = z.infer<typeof cashierFormSchema>

  const form = useForm<CashierFormData>({
    resolver: zodResolver(cashierFormSchema),
    defaultValues: formDefaultValues,
  })

  const clearForm = useCallback(() => {
    form.reset(formDefaultValues)
    form.setValue(BUYER_MOBILE_NUMBER.KEY, "")
    setSelectedListings([])
    setBuyerData(null)
    setBuyerAddress(null)
    setProcessingFeeAmount(0)

    queryClient.removeQueries({
      queryKey: [QueryKeys.GET_ALL_SELLER_LISTING],
    })
    clearSellerListing()
  }, [
    form,
    setSelectedListings,
    setBuyerData,
    setBuyerAddress,
    setProcessingFeeAmount,
    queryClient,
    clearSellerListing,
  ])

  const mutation = useCashierFormMutation({
    onSuccess: () => {
      clearForm()
    },
  })

  const shippingMethod = useMemo(
    () => ({
      isDelivery:
        form.watch(SHIPPING_METHOD.KEY) === ECashierShippingMethod.Delivery,
      isStorePickup:
        form.watch(SHIPPING_METHOD.KEY) === ECashierShippingMethod.StorePickUp,
      value: form.watch(SHIPPING_METHOD.KEY),
    }),
    [form],
  )

  const transformPayload = (data: CashierFormData) => {
    const getCreatedSource = () => {
      if (cashierType.isStore) return data[WAREHOUSE.KEY]?.label
      if (cashierType.isMarketplace) return data[MARKETPLACE.KEY]?.label
      if (cashierType.isEvent) return data[EVENT.KEY]
      return ""
    }

    const getShippingAddress = () => {
      const shippingAddressMain = `${data[ADDRESS.KEY]}, ${data[NOTES.KEY]}`
      const shippingAddressJoin = [
        data[DISTRICT.KEY]?.label,
        data[CITY.KEY]?.label,
        data[PROVINCE.KEY]?.label,
        data[POSTAL_CODE.KEY],
      ].join(", ")
      const fullShippingAddress = `${shippingAddressMain}, ${shippingAddressJoin}`

      if (shippingMethod.isDelivery) {
        return fullShippingAddress
      } else {
        return getCreatedSource()
      }
    }

    const getPhoneNumber = () => {
      const buyerCountryCode = data[BUYER_MOBILE_COUNTRY_NUMBER.KEY]?.label
      const buyerPhoneNumber = data[BUYER_MOBILE_NUMBER.KEY]
      const fullBuyerPhoneNumber = `${buyerCountryCode}${buyerPhoneNumber?.replace(buyerCountryCode, "")}`

      if (shippingMethod.isDelivery) {
        return ""
      } else {
        return fullBuyerPhoneNumber
      }
    }

    const getPostalCode = () => {
      if (shippingMethod.isDelivery) {
        return data[POSTAL_CODE.KEY]?.value || ""
      } else {
        return data[WAREHOUSE.KEY]?.value?.split("-")?.[1] || ""
      }
    }

    const paymentMethodId = data[PAYMENT_METHOD.KEY]?.value.split("-")?.[0] ?? 0

    const payload: any = {
      createdSource: getCreatedSource(),
      shippingMethod: data[SHIPPING_METHOD.KEY],
      buyerId: Number(data[BUYER_EMAIL.KEY]?.value || 0),
      paymentMethodId: Number(paymentMethodId),
      listingIds: selectedListings.map((listing) => listing.id),
      shippingAddress: getShippingAddress(),
      phoneNumber: getPhoneNumber(),
      voucherId: data[VOUCHER_CODE.KEY]?.value,
      remark: data[REMARK.KEY],
      referenceNumber: data[REFERENCE_NUMBER.KEY],
      postalCode: getPostalCode(),
      isUsingCreditBalance: data[CREDIT_ENABLED.KEY],
      isUsingKickPoint: data[KICK_POINT_ENABLED.KEY],
    }

    return payload
  }

  const onNoOneProductChosen = () => {
    setShowToast(true, "Please choose minimum one product.", "danger")
  }

  const onFormValid = (data: CashierFormData) => {
    // eslint-disable-next-line no-console
    console.log("onFormValid ", data)

    if (selectedListings.length === 0) {
      onNoOneProductChosen()
      return
    }

    const payload = transformPayload(data)
    mutation.mutate(payload)
  }

  const onFormInvalid = (errors: FieldErrors<any>) => {
    form.setValue(WAREHOUSE.KEY, null)

    // eslint-disable-next-line no-console
    console.log("onFormInvalid errors ", errors)

    if (selectedListings.length === 0) {
      onNoOneProductChosen()
    }
  }

  useEffect(() => {
    clearForm()
  }, [pathname, clearForm])

  return { form, onFormValid, onFormInvalid, shippingMethod }
}

export default useCashierForm
