import { CheckBox, Label, Text } from "@kickavenue/ui/dist/src/components"
import { Controller, useFormContext } from "react-hook-form"

import { FormField } from "@constants/formField"
import { ECashierShippingMethod } from "types/cashier.type"

const { SHIPPING_METHOD } = FormField.CASHIER
const { StorePickUp, Delivery } = ECashierShippingMethod

const CashierPaymentDetailFieldsShipping = () => {
  const { control, setValue } = useFormContext()

  return (
    <div className="flex flex-col gap-sm">
      <Label size="sm" state="required" type="default">
        Shipping Method
      </Label>
      <Controller
        name={SHIPPING_METHOD.KEY}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[SHIPPING_METHOD.KEY]?.message as string

          return (
            <div className="flex flex-col gap-xs">
              <div className="flex justify-between">
                <CheckBox
                  {...field}
                  id={`${SHIPPING_METHOD.KEY}_pickup`}
                  className="flex-1"
                  label="Store Pick-Up"
                  type="radio"
                  checked={field.value === StorePickUp}
                  onChange={() =>
                    setValue(SHIPPING_METHOD.KEY, StorePickUp, {
                      shouldValidate: true,
                    })
                  }
                />
                <CheckBox
                  {...field}
                  id={`${SHIPPING_METHOD.KEY}_delivery`}
                  className="flex-1"
                  label="Delivery"
                  type="radio"
                  checked={field.value === Delivery}
                  onChange={() =>
                    setValue(SHIPPING_METHOD.KEY, Delivery, {
                      shouldValidate: true,
                    })
                  }
                />
              </div>

              {errorMessage && (
                <Text size="xs" state="danger" type="regular">
                  {errorMessage}
                </Text>
              )}
            </div>
          )
        }}
      />
    </div>
  )
}

export default CashierPaymentDetailFieldsShipping
