import { AsyncCombobox, Label, Text } from "@kickavenue/ui/components"
import debounce from "lodash/debounce"
import { useCallback, useEffect, useMemo } from "react"
import { Controller, useFormContext } from "react-hook-form"

import useFetchMemberDetail from "@app/hooks/useFetchMemberDetail"
import { GetAllMembers } from "@application/usecases/GetAllMembers"
import { FormField } from "@constants/formField"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { useCashierFormStore } from "stores/cashierFormStore"
import { TMemberFilter } from "types/member.type"

import usePopulateAddressForm from "../hooks/usePopulateAddressForm"

const { BUYER_EMAIL, RECIPIENT_NAME } = FormField.CASHIER

const CashierPaymentDetailFieldsBuyerEmail = () => {
  const { setBuyerData } = useCashierFormStore()
  const { control, watch, setValue } = useFormContext()
  const buyerEmail = watch(BUYER_EMAIL.KEY)
  const buyerEmailValue = useMemo(() => buyerEmail?.value, [buyerEmail?.value])

  // START === debounce and fetch members ====================================
  const fetchMembers = async (filter: TMemberFilter) => {
    const r = new MemberApiRepository()
    const u = new GetAllMembers(r)
    const res = await u.execute(filter)
    return res
  }

  const debouncedFetchMembers = debounce(
    async (search: string, callback: any, page: number) => {
      const data = await fetchMembers({
        page,
        pageSize: 10,
        sort: [],
        ...(search && { search }),
      })
      callback({
        options:
          data?.content.map((member) => ({
            value: String(member.id),
            label: `${member.firstName} ${member.lastName} - ${member.email}`,
          })) || [],
        hasMore: !data?.last,
        additional: { page: page + 1 },
      })
    },
    500,
  )

  const loadOptions = useCallback(
    async (search: string, opt: any, additional: any) => {
      const dataPage = additional?.page ?? 0

      return new Promise((resolve) => {
        debouncedFetchMembers(search, resolve, dataPage)
      })
    },
    [debouncedFetchMembers],
  )
  // END === debounce and fetch members ====================================

  // START === set buyer data ==============================================
  const { data: buyerData } = useFetchMemberDetail(buyerEmailValue)

  useEffect(() => {
    if (!buyerData) return

    setBuyerData(buyerData)
    setValue(
      RECIPIENT_NAME.KEY,
      `${buyerData?.firstName ?? ""} ${buyerData?.lastName ?? ""}`.trim(),
      { shouldValidate: true },
    )
  }, [buyerData, setValue, setBuyerData])
  // END === set buyer data ================================================

  // START === set buyer address ===========================================
  usePopulateAddressForm(buyerData?.id)
  // END === set buyer address =============================================

  return (
    <Controller
      name={BUYER_EMAIL.KEY}
      control={control}
      render={({ field, formState: { errors } }) => {
        const errorMessage = errors[BUYER_EMAIL.KEY]?.message as string

        return (
          <div className="flex flex-col gap-y-sm">
            <Label state="required" size="sm" type="default">
              {BUYER_EMAIL.LABEL}
            </Label>
            <div className="flex flex-col gap-y-xs">
              <AsyncCombobox
                {...field}
                additional={{ page: 0 }}
                loadOptions={loadOptions as any}
                placeholder={BUYER_EMAIL.PLACEHOLDER}
                isClearable
              />
              {errorMessage && (
                <Text size="xs" state="danger" type="regular">
                  {errorMessage}
                </Text>
              )}
            </div>
          </div>
        )
      }}
    />
  )
}

export default CashierPaymentDetailFieldsBuyerEmail
