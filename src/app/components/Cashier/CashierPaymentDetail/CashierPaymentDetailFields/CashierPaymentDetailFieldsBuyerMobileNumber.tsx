/* eslint-disable max-lines-per-function */

import { ComboBox, Input, Label, Text } from "@kickavenue/ui/components"
import { TItemOption } from "@kickavenue/ui/components/ComboBox/ComboBox.type"
import Image from "next/image"
import { useCallback, useEffect, useMemo } from "react"
import { Controller, useFormContext } from "react-hook-form"

import useFetchCountryById from "@app/hooks/useFetchCountryById"
import useFetchCountryOptionsPaginate from "@app/hooks/useFetchCountryOptionsPaginate"
import { FormField } from "@constants/formField"
import { getItemCloudfrontImageUrl } from "@utils/image.utils"
import { useCashierFormStore } from "stores/cashierFormStore"
import { TCountry } from "types/country.type"

import useCashierForm from "../hooks/useCashierForm"

const { BUYER_MOBILE_COUNTRY_NUMBER, BUYER_MOBILE_NUMBER } = FormField.CASHIER

const prettierCountryFlagItem = (c: TCountry) => {
  return {
    label: c.prefix,
    value: String(c.country),
    icon: c.flag ? (
      <Image
        height={16}
        width={16}
        src={getItemCloudfrontImageUrl(c.flag)}
        alt={c.name || ""}
      />
    ) : null,
  } as TItemOption
}

const CashierPaymentDetailFieldsBuyerMobileNumber = () => {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()
  const { data: countryOptions } = useFetchCountryOptionsPaginate()
  const { buyerData } = useCashierFormStore()
  const { data: countryDetail } = useFetchCountryById(buyerData?.countryId ?? 0)
  const { shippingMethod } = useCashierForm()
  const countries = countryOptions?.content
  const buyerMobileCountryNumber = Number(
    watch(BUYER_MOBILE_COUNTRY_NUMBER.KEY)?.value || 0,
  )
  const errorMobileCountryNumber = errors[BUYER_MOBILE_COUNTRY_NUMBER.KEY]
    ?.message as string
  const errorMobileNumber = errors[BUYER_MOBILE_NUMBER.KEY]?.message as string
  const errorMessage = useMemo(() => {
    if (errorMobileCountryNumber || errorMobileNumber)
      return errorMobileCountryNumber || errorMobileNumber
  }, [errorMobileCountryNumber, errorMobileNumber])

  const comboBoxItems =
    (countries?.map((c: TCountry) =>
      prettierCountryFlagItem(c),
    ) as TItemOption[]) || []

  const comboBoxSelected = useMemo(() => {
    const selectedCountry = countries?.find(
      (c) => c.country === buyerMobileCountryNumber,
    )

    if (!selectedCountry?.country) return null

    return prettierCountryFlagItem(selectedCountry)
  }, [countries, buyerMobileCountryNumber])

  const handleComboBoxChange = useCallback(
    (val: TItemOption | null) => {
      setValue(BUYER_MOBILE_COUNTRY_NUMBER.KEY, val, { shouldValidate: true })
    },
    [setValue],
  )

  const transformPhoneNumber = useCallback(
    (value: string) => {
      return value
        .replace(countryDetail?.prefix || "", "")
        .replace(comboBoxSelected?.label || "", "")
        .replace(/\D/g, "")
    },
    [countryDetail, comboBoxSelected],
  )

  const handlePhoneNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = transformPhoneNumber(e.target.value)
      setValue(BUYER_MOBILE_NUMBER.KEY, value, { shouldValidate: true })
    },
    [setValue, transformPhoneNumber],
  )

  useEffect(() => {
    if (countryDetail) {
      setValue(
        BUYER_MOBILE_COUNTRY_NUMBER.KEY,
        {
          label: countryDetail?.prefix ?? "",
          value: String(countryDetail?.country ?? ""),
        },
        { shouldValidate: true },
      )
    } else {
      setValue(BUYER_MOBILE_COUNTRY_NUMBER.KEY, null)
    }
  }, [countryDetail, setValue])

  useEffect(() => {
    if (!buyerData?.countryId) return

    const selectedCountry = countries?.find(
      (c) => c.country === buyerData.countryId,
    )

    setValue(
      BUYER_MOBILE_COUNTRY_NUMBER.KEY,
      {
        label: selectedCountry?.prefix ?? "",
        value: String(selectedCountry?.country ?? ""),
      },
      { shouldValidate: true },
    )
  }, [buyerData?.countryId, countries, setValue])

  useEffect(() => {
    if (!buyerData?.phoneNumber) return

    setValue(
      BUYER_MOBILE_NUMBER.KEY,
      buyerData?.phoneNumber
        ? transformPhoneNumber(buyerData?.phoneNumber)
        : "",
      { shouldValidate: true },
    )
  }, [buyerData?.phoneNumber, transformPhoneNumber, setValue])

  return (
    <div className="flex flex-col gap-y-sm">
      <Label state="required" size="sm" type="default">
        {BUYER_MOBILE_NUMBER.LABEL}
      </Label>
      <div className="flex gap-xs">
        <Controller
          name={BUYER_MOBILE_COUNTRY_NUMBER.KEY}
          control={control}
          render={({ field }) => (
            <ComboBox
              {...field}
              items={comboBoxItems}
              selected={comboBoxSelected}
              setSelected={handleComboBoxChange}
              className="!max-w-28"
              iconLeading={comboBoxSelected?.icon}
              placeholder={BUYER_MOBILE_COUNTRY_NUMBER.PLACEHOLDER}
              disabled={shippingMethod.isDelivery}
            />
          )}
        />

        <Controller
          name={BUYER_MOBILE_NUMBER.KEY}
          control={control}
          render={({ field }) => (
            <div className="w-full">
              <Input
                {...field}
                value={transformPhoneNumber(field.value)}
                placeholder={BUYER_MOBILE_NUMBER.PLACEHOLDER}
                type="text"
                onChange={handlePhoneNumberChange}
                maxLength={12}
                disabled={shippingMethod.isDelivery}
              />
            </div>
          )}
        />
      </div>

      {errorMessage && (
        <Text size="xs" state="danger" type="regular">
          {errorMessage}
        </Text>
      )}
    </div>
  )
}

export default CashierPaymentDetailFieldsBuyerMobileNumber
