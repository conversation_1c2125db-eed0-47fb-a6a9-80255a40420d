import { Controller, useFormContext } from "react-hook-form"

import Input from "@components/shared/Form/Input"
import { FormField } from "@constants/formField"

const { EVENT } = FormField.CASHIER

const CashierPaymentDetailFieldsEvent = () => {
  const { control } = useFormContext()
  return (
    <Controller
      control={control}
      name={EVENT.KEY}
      render={({ field, formState: { errors } }) => {
        const errorMessage = errors[EVENT.KEY]?.message as string
        const variant = errorMessage ? "danger" : undefined

        return (
          <Input
            {...field}
            label={EVENT.LABEL}
            placeholder={EVENT.PLACEHOLDER}
            state="required"
            helperText={errorMessage}
            variant={variant}
          />
        )
      }}
    />
  )
}

export default CashierPaymentDetailFieldsEvent
