/* eslint-disable max-lines-per-function */
"use client"

import Label from "@kickavenue/ui/dist/src/components/Label/Label"
import { Controller, useFormContext } from "react-hook-form"

import Input from "@components/shared/Form/Input"
import Textarea from "@components/shared/Form/Textarea"
import SelectCity from "@components/shared/Select/SelectCity"
import SelectDistrict from "@components/shared/Select/SelectDistrict"
import SelectPostalCode from "@components/shared/Select/SelectPostalCode"
import SelectProvince from "@components/shared/Select/SelectProvince"
import { FormField } from "@constants/formField"
import { ECashierShippingMethod } from "types/cashier.type"
import { TSelectOption } from "types/misc.type"

const {
  SHIPPING_METHOD,
  RECIPIENT_NAME,
  PROVINCE,
  CITY,
  DISTRICT,
  POSTAL_CODE,
  ADDRESS,
  NOTES,
} = FormField.CASHIER

const CashierPaymentDetailFieldsDelivery = () => {
  const { watch, register, control, setValue } = useFormContext()
  const shippingMethod = watch(SHIPPING_METHOD.KEY)
  const provinceLabel = watch(PROVINCE.KEY)?.label
  const cityLabel = watch(CITY.KEY)?.label
  const districtLabel = watch(DISTRICT.KEY)?.label

  const handleProvinceChange = (val: TSelectOption | null) => {
    if (!val) {
      setValue(PROVINCE.KEY, null)
      setValue(CITY.KEY, null)
      setValue(DISTRICT.KEY, null)
      setValue(POSTAL_CODE.KEY, null)
      return
    }

    setValue(PROVINCE.KEY, val)
  }

  const handleCityChange = (val: TSelectOption | null) => {
    if (!val) {
      setValue(CITY.KEY, null)
      setValue(DISTRICT.KEY, null)
      setValue(POSTAL_CODE.KEY, null)
      return
    }

    setValue(CITY.KEY, val)
  }

  const handleDistrictChange = (val: TSelectOption | null) => {
    if (!val) {
      setValue(DISTRICT.KEY, null)
      setValue(POSTAL_CODE.KEY, null)
      return
    }

    setValue(DISTRICT.KEY, val)
  }

  if (shippingMethod !== ECashierShippingMethod.Delivery) return null

  return (
    <>
      <Input
        name={RECIPIENT_NAME.KEY}
        label={RECIPIENT_NAME.LABEL}
        placeholder={RECIPIENT_NAME.PLACEHOLDER}
        state="required"
        register={register}
      />

      <div className="flex flex-col gap-sm">
        <Label size="sm" state="required" type="default">
          {PROVINCE.LABEL}
        </Label>
        <Controller
          name={PROVINCE.KEY}
          control={control}
          render={({ field }) => (
            <SelectProvince
              {...field}
              onChange={handleProvinceChange}
              defaultOptions
              placeholder={PROVINCE.PLACEHOLDER}
            />
          )}
        />
      </div>

      <div className="flex flex-col gap-sm">
        <Label size="sm" state="required" type="default">
          {CITY.LABEL}
        </Label>
        <Controller
          name={CITY.KEY}
          control={control}
          render={({ field }) => (
            <SelectCity
              {...field}
              onChange={handleCityChange}
              provinceName={provinceLabel}
              placeholder={CITY.PLACEHOLDER}
            />
          )}
        />
      </div>

      <div className="flex flex-col gap-sm">
        <Label size="sm" state="required" type="default">
          {DISTRICT.LABEL}
        </Label>
        <Controller
          name={DISTRICT.KEY}
          control={control}
          render={({ field }) => (
            <SelectDistrict
              {...field}
              onChange={handleDistrictChange}
              cityName={cityLabel}
              placeholder={DISTRICT.PLACEHOLDER}
            />
          )}
        />
      </div>

      <div className="flex flex-col gap-sm">
        <Label size="sm" state="required" type="default">
          {POSTAL_CODE.LABEL}
        </Label>
        <Controller
          name={POSTAL_CODE.KEY}
          control={control}
          render={({ field }) => (
            <SelectPostalCode
              {...field}
              districtName={districtLabel}
              placeholder={POSTAL_CODE.PLACEHOLDER}
              optionMapper={(postalCode) => ({
                value: postalCode.code,
                label: postalCode.code,
              })}
            />
          )}
        />
      </div>

      <Controller
        name={ADDRESS.KEY}
        control={control}
        render={({ field }) => (
          <Textarea
            {...field}
            name={ADDRESS.KEY}
            label={ADDRESS.LABEL}
            placeholder={ADDRESS.PLACEHOLDER}
            state="required"
          />
        )}
      />

      <Input
        name={NOTES.KEY}
        label={NOTES.LABEL}
        placeholder={NOTES.PLACEHOLDER}
        state="required"
        register={register}
      />
    </>
  )
}

export default CashierPaymentDetailFieldsDelivery
