import { useMemo } from "react"
import { Controller, useFormContext } from "react-hook-form"

import useFetchPaymentMethod from "@app/hooks/useFetchPaymentMethod"
import ComboBox from "@components/shared/Form/ComboBox"
import { FormField } from "@constants/formField"
import { TPaymentMethod } from "types/paymentMethod.type"

const { PAYMENT_METHOD, CREDIT_ENABLED, KICK_POINT_ENABLED } = FormField.CASHIER

const CashierPaymentDetailFieldsPaymentMethod = () => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext()
  const { data } = useFetchPaymentMethod({ page: 0, pageSize: 100 })

  const paymentMethodOptions = useMemo(() => {
    return data?.content.map((item: TPaymentMethod) => ({
      label: item.name,
      value: `${item.id}-${item.type}`,
    }))
  }, [data])

  const onClearPaymentMethod = () => {
    setValue(CREDIT_ENABLED.KEY, false)
    setValue(KICK_POINT_ENABLED.KEY, false)
  }

  return (
    <Controller
      name={PAYMENT_METHOD.KEY}
      control={control}
      render={({ field }) => {
        const errorMessage = errors[PAYMENT_METHOD.KEY]?.message as string
        const variant = errorMessage ? "danger" : undefined

        return (
          <ComboBox
            {...field}
            label={PAYMENT_METHOD.LABEL}
            setSelected={(val) => {
              field.onChange(val)
              if (val === null) onClearPaymentMethod()
            }}
            selected={field.value}
            items={paymentMethodOptions}
            placeholder={PAYMENT_METHOD.PLACEHOLDER}
            state="required"
            helperText={errorMessage}
            variant={variant}
          />
        )
      }}
    />
  )
}

export default CashierPaymentDetailFieldsPaymentMethod
