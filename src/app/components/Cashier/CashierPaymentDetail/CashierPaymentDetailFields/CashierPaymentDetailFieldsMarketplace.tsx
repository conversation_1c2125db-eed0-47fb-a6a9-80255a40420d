import { TItemOption } from "@kickavenue/ui/components/ComboBox/ComboBox.type"
import { Controller, useFormContext } from "react-hook-form"

import ComboBox from "@components/shared/Form/ComboBox"
import { FormField } from "@constants/formField"
import { ECashierMarketplace } from "types/cashier.type"

const { MARKETPLACE } = FormField.CASHIER

const marketplaceItems = Object.entries(ECashierMarketplace).map(
  ([label, value]) => ({
    label,
    value,
  }),
)

const CashierPaymentDetailFieldsMarketplace = () => {
  const { control } = useFormContext()

  return (
    <Controller
      control={control}
      name={MARKETPLACE.KEY}
      render={({ field, formState: { errors } }) => {
        const errorMessage = errors[MARKETPLACE.KEY]?.message as string
        const variant = errorMessage ? "danger" : undefined

        return (
          <ComboBox
            {...field}
            selected={field.value}
            setSelected={(val: TItemOption | null) => {
              field.onChange(val, { shouldValidate: true })
            }}
            label={MARKETPLACE.LABEL}
            placeholder={MARKETPLACE.PLACEHOLDER}
            state="required"
            items={marketplaceItems}
            helperText={errorMessage}
            variant={variant}
          />
        )
      }}
    />
  )
}

export default CashierPaymentDetailFieldsMarketplace
