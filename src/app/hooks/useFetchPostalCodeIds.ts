import { useQuery } from "@tanstack/react-query"

import { QueryKeys } from "@constants/queryKeys"
import { PostalCodeApiRepository } from "@infrastructure/repositories/postalCodeApiRepository"

const useFetchPostalCodeIds = (params?: any) => {
  const fetchPostalCodeIds = async () => {
    const r = new PostalCodeApiRepository()
    const res = await r.getIds(params)
    return res
  }

  return useQuery({
    queryKey: [
      QueryKeys.DISTRICT_BY_ID,
      ...Object.entries(params).map(([key, value]) => `${key}-${value}`),
    ],
    queryFn: fetchPostalCodeIds,
    enabled: Boolean(params),
  })
}

export default useFetchPostalCodeIds
