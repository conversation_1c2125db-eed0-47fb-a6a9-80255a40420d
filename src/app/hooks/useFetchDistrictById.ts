import { useQuery } from "@tanstack/react-query"

import { QueryKeys } from "@constants/queryKeys"
import { DistrictApiRepository } from "@infrastructure/repositories/districtApiRepository"

const useFetchDistrictById = (districtId?: string | number) => {
  const fetchDistrictById = async () => {
    const r = new DistrictApiRepository()
    const res = await r.getById(districtId)
    return res
  }

  return useQuery({
    queryKey: [QueryKeys.DISTRICT_BY_ID, districtId],
    queryFn: fetchDistrictById,
    enabled: Boolean(districtId),
  })
}

export default useFetchDistrictById
