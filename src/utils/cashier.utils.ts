import { DropdownItemProps } from "@kickavenue/ui/dist/src/components"

import { MiscConstant } from "@constants/misc"
import { ItemCondition, PackagingCondition, Price } from "types/misc.type"
import {
  ESellerListingQuantityType,
  ESellerListingShippingMethod,
  TSellerListing,
} from "types/sellerListing.type"
import { TVoucher } from "types/voucher.type"

import { formatStringWithSpaces } from "./misc"

export function getShippingOptions(
  selected = "all-shipping-method",
): DropdownItemProps[] {
  return [
    {
      text: "All Shipping Method",
      value: "all-shipping-method",
    },
    {
      text: "Express",
      value: ESellerListingShippingMethod.Express,
    },
    {
      text: "Standard",
      value: ESellerListingShippingMethod.Standard,
    },
    {
      text: "Pre-order",
      value: ESellerListingShippingMethod.PreOrder,
    },
  ].map((option) => ({
    ...option,
    checkmark: option.value === selected,
  }))
}

export function getSizeOptions(
  selected = "0",
  items: DropdownItemProps[] = [],
): DropdownItemProps[] {
  return [
    {
      text: "All Size",
      value: "0",
    },
    ...items,
  ].map((option) => ({
    ...option,
    checkmark: option.value === selected,
  }))
}

export function getItemConditionOptions(
  selected = "all-item-condition",
): DropdownItemProps[] {
  const itemConditionOptions = Object.values(ItemCondition).map(
    (condition) => ({
      text: formatStringWithSpaces(condition),
      value: condition,
    }),
  )
  return [
    {
      text: "All Item Condition",
      value: "all-item-condition",
    },
    ...itemConditionOptions,
  ].map((option) => ({
    ...option,
    checkmark: option.value === selected,
  }))
}

export function getPackagingConditionOptions(
  selected = "all-packaging-condition",
): DropdownItemProps[] {
  const packagingConditionOptions = Object.values(PackagingCondition).map(
    (condition) => ({
      text: formatStringWithSpaces(condition),
      value: condition,
    }),
  )
  return [
    {
      text: "All Packaging Condition",
      value: "all-packaging-condition",
    },
    ...packagingConditionOptions,
  ].map((option) => ({
    ...option,
    checkmark: option.value === selected,
  }))
}

export function getHasQuantityOptions(
  selected = ESellerListingQuantityType.HasQuantity,
): DropdownItemProps[] {
  return [
    {
      text: "Has Quantity",
      value: ESellerListingQuantityType.HasQuantity,
    },
    {
      text: "No Quantity",
      value: ESellerListingQuantityType.NoQuantity,
    },
  ].map((option) => ({
    ...option,
    checkmark: option.value === selected,
  }))
}

export function sumSelectedListings(listings?: TSellerListing[]) {
  if (!listings) return 0
  const sumMinUnitVal = listings?.reduce((acc, listing) => {
    return acc + listing.sellingPrice.minUnitVal
  }, 0)
  return sumMinUnitVal / MiscConstant.PRICE_STRIPE_DEVIDER
}

export function generateVoucherValue({
  id,
  amount,
  maxDiscountAmount,
}: TVoucher) {
  return [
    id,
    (amount as Price)?.minUnitVal,
    (maxDiscountAmount as Price)?.minUnitVal,
  ].join("_")
}

export function getPurchaseValue(data: any, isMinus = false) {
  if (!data) return 0
  if (typeof data === "string" && !isNaN(Number(data))) {
    return isMinus ? Number(data) * -1 : Number(data)
  }
  return isMinus ? data * -1 : data
}

export function calcCreditUsage(totalItems: number, totalCredit: number) {
  return totalItems > totalCredit ? totalCredit : totalItems
}
