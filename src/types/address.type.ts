import { TListParams } from "./apiResponse.type"

export interface TAddress {
  id?: number
  memberId?: number
  title?: string | null
  isPrimary?: boolean
  address?: string | null
  addressDetail?: string
  cityId?: number | null
  countryId?: number | null
  provinceId?: number | null
  regionId?: string | null
  districtId?: string | null
  zipCode?: string | null
  notes?: string
  longitude?: number | null
  latitude?: number | null
  geoReverseAddress?: string
  phoneNumber?: string
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string

  // for ui purpose
  mobileCode?: string
}

export interface TAddressFormData {
  title: string
  isPrimary: boolean
  address: string
  addressDetail?: string
  cityId: number
  countryId: number
  provinceId: number
  regionId: string
  zipCode: string
  notes?: string
  longitude: number
  latitude: number
  phoneNumber: string
}

export interface TAddressFilter extends TListParams {
  memberId?: number
}
