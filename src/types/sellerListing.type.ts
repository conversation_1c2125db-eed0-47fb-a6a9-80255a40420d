import { TListParams } from "./apiResponse.type"
import { TItem } from "./item.type"
import { TItemCondition } from "./listing.type"
import { TMember } from "./member.type"
import { PreOrderDuration, Price, SellerListingStatus } from "./misc.type"
import { TSize } from "./size.type"

export interface DefectDetail {
  image?: (File | string)[] | string
  name: string
  type: string
  info: string
}

export interface DefectDetailFormItem {
  image?: (File | string)[] | string
  name: string
  info: string
  enabled: boolean
}

export interface DefectDetailForm {
  type: string
  details: DefectDetailFormItem[]
}

export interface Inclusion {
  category: string
  name: string
  value: boolean
}

export interface TSellerListing {
  id: number
  item: TItem
  consignmentId: string
  purchasePrice: Price
  sellingPrice: Price
  quantity: number
  expiryDate: string
  itemCondition: string
  packagingCondition: string
  isPreOrder: boolean
  preOrderDuration: PreOrderDuration
  preOrderAmount: number
  isConsignment: boolean
  overallAppearanceImage?: string[]
  isNewNoDefect: boolean
  defectDetail: DefectDetail[]
  inclusion?: Inclusion[]
  note: string
  size: TSize
  status: SellerListingStatus
  isOffline: boolean
  userId: number
  user: TMember
  highestOfferAmount: number
  isHypequarterDisplay: boolean
  images: string[]
  approvedAt: string
  approvedBy: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  priceUpdatedAt: string
  priceUpdatedBy: string
  seller?: TMember
  rackName?: string
}

export enum ESellerListingQuantityType {
  HasQuantity = "has-quantity",
  NoQuantity = "no-quantity",
}

export interface TSellerListingFilter extends TListParams {
  ids?: number[]
  status?: string
  shippingMethod?: string
  sellerEmail?: string | string[]
  itemCondition?: string | string[]
  packagingCondition?: string | string[]
  size?: string
  rack?: string
  categoryName?: string | string[]
  subCategoryName?: string | string[]
  itemName?: string
  sizeId?: number[]
  hasQuantity?: boolean
  user?: TMember
  isNewNoDefect?: boolean

  // for cashier handling filter
  quantityValue?: ESellerListingQuantityType
}

export interface TSellerListingFormData {
  countryId: number
  isConsignment: boolean
  isNewNoDefect: boolean
  isOffline: boolean
  isPreOrder: boolean
  itemCondition?: string
  itemId: number
  packagingCondition?: string
  quantity: number
  sellerId: number
  sellingPrice: number
  sizeId: number
  status?: SellerListingStatus
  consignmentId: string
  defectDetail: DefectDetail[]
  expiryDate: string
  inclusion: Inclusion[]
  note: string
  preOrderDuration?: PreOrderDuration
  purchasePrice: number
  rackId: number
  isHypequarterDisplay: boolean
  overallAppearanceImage?: File[]
}

export interface TSellerListingForm {
  itemId: number
  sizeId: number
  purchasePrice: number
  sellingPrice: number
  countryId: number
  quantity: number
  expiryDate: string
  itemCondition: TItemCondition
  packagingCondition: string
  isPreOrder: boolean
  preOrderDuration?: string
  isConsignment: boolean
  defectDetail: DefectDetailForm[]
  inclusion: Inclusion[]
  note?: string
  status: string
  isOffline: boolean
  isHypequarterDisplay: boolean
  approvedAt?: string
  approvedBy?: string
  userId?: number
  overallAppearanceImage?: File[]
}

export type TSellerListingKeyed = Record<number, TSellerListing>

export interface TSellerListingAdditionalState {
  totalPages: number
  selectedStatus?: SellerListingStatus | null
  selectedRows?: TSellerListingKeyed | null
}

export enum ESellerListingShippingMethod {
  Express = "express",
  PreOrder = "pre-order",
  Standard = "standard",
}
