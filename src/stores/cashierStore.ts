import { create } from "zustand"

import { TSellerListing, TSellerListingFilter } from "types/sellerListing.type"

interface CashierStore {
  sellerListing: {
    selectedListings: TSellerListing[]
    selectedRowKeys: number[]
    filter: TSellerListingFilter
  }
}

interface CashierAction {
  setSelectedListings: (listings: TSellerListing[]) => void
  setSellerListingSelectedRowKeys: (selectedRowKeys: number[]) => void
  setSellerListingFilter: (filter: TSellerListingFilter) => void
  clearSellerListingFilter: () => void
  clearSellerListing: () => void
}

export const useCashierStore = create<CashierStore & CashierAction>(
  (set, get) => ({
    sellerListing: {
      selectedRowKeys: [],
      selectedListings: [],
      filter: {
        hasQuantity: true,
      },
    },
    setSellerListingSelectedRowKeys: (selectedRowKeys) =>
      set({
        sellerListing: {
          ...get().sellerListing,
          selectedRowKeys,
        },
      }),
    setSellerListingFilter: (filter) =>
      set({
        sellerListing: {
          ...get().sellerListing,
          filter,
        },
      }),
    setSelectedListings: (listings) =>
      set({
        sellerListing: {
          ...get().sellerListing,
          selectedListings: listings,
        },
      }),
    clearSellerListingFilter: () =>
      set({
        sellerListing: {
          ...get().sellerListing,
          filter: {
            hasQuantity: true,
          },
        },
      }),
    clearSellerListing: () =>
      set({
        sellerListing: {
          ...get().sellerListing,
          selectedListings: [],
          selectedRowKeys: [],
        },
      }),
  }),
)
